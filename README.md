# SmartB WebApp Admin

A web-based admin dashboard for managing SmartB platform features, users, and content.

## Table of Contents

- [About](#about)
- [Features](#features)
- [Project Structure](#project-structure)
- [Getting Started](#getting-started)
- [Configuration](#configuration)
- [Available Scripts](#available-scripts)
- [Testing](#testing)
- [Deployment](#deployment)
- [Contributing](#contributing)
- [Contact](#contact)

## About

SmartB WebApp Admin is an administrative interface for the SmartB platform, providing tools for content management, user administration, and analytics.

## Features

- Sports Details management
- Racing management
  - Race odds
  - All races overview
  - Horse Racing
    - Horses management
    - Jockeys management
    - Trainers management
  - Harness Racing
  - Greyhound Racing
  - Tracks management
- Sports management
  - American Football
  - Australian Rules
  - Basketball
  - Baseball
  - Boxing
  - Cricket
    - Odds Dashboard
    - Category
    - Tournaments
    - Unique Tournament
    - Team
    - Player
    - Event
    - Market
    - Market Error
    - Event Error
    - Fixture Import
    - Automation
    - Label
    - Rules
    - Squad
  - Golf
  - Ice Hockey
  - Mixed Martial Arts
  - Rugby League
  - Rugby Union
  - Soccer
  - Tennis
- Master Data
  - Country
  - Sport List
- Bookkeeper
  - Bookkeeper List
  - Bookkeeper Review
- News
  - News Article
  - News Category
  - News Tags
- User Management
  - Dashboard
  - Subscriber Dashboard
  - SOC SmartPlay Dashboard
  - User List
- Campaign
  - Clients
  - Ad Campaign
- Our Team
  - Team Details
  - Team Category
  - Team Positions
- Racing Expert Tips
  - Expert Tips
  - Tips Of The Day
  - BAW Featured Race
  - Weekly Newsletter
- Sport Expert Tips
  - Expert Tips
- Tipping Comp
  - Tipping Comp Tips
  - Tipping Comp FAQs
  - Tipping Comp List
  - Tipping Comp Prize
  - Premium Comp Fees
- Subscriptions
  - Subscriptions Active
  - Subscriptions Coupon
  - Subscriptions Dashboard
- SOC
  - Contact Us
  - SOC Broadcast
  - Testimonial
- Fantasy
  - Coins
  - KYC
  - User Coins
  - Withdraw
- Advertising Screen
- News Import
- Sponsored Odds
- Sponsored Logo
- Bookkeeper Counter
- Featured Race
- Featured Sports
- Recommended Website

## Project Structure

```
smartb-webapp-admin/
  ├── Dockerfile
  ├── Jenkinsfile
  ├── package.json
  ├── package-lock.json
  ├── README.md
  ├── public/
  │   ├── favicon.ico
  │   ├── index.html
  │   └── ...
  ├── src/
  │   ├── admin/
  │   │   ├── adCampaign/
  │   │   ├── adminlogin/
  │   │   ├── AdvertisementSection/
  │   │   └── ... (many feature folders)
  │   ├── assets/
  │   ├── components/
  │   ├── helpers/
  │   ├── images/
  │   ├── services/
  │   ├── store/
  │   └── index.js
  └── ... (other files and folders)
```

## Getting Started

### Prerequisites

- Node.js (v14 or above recommended)
- npm (v6 or above)

### Installation

Clone the repository and install dependencies:

```bash
git clone <repo-url>
cd smartb-webapp-admin
npm install
```

## Configuration

- Update any environment variables or configuration files as needed (e.g., `.env`).

```env
 REACT_APP_API_BASE_URL = https://staging.smartb.au.sydney.digiground.com.au/api/
REACT_APP_BASE_NAME = /admin
REACT_APP_BASE_URL = https://staging.smartb.au.sydney.digiground.com.au/admin/
REACT_APP_WP_BASE_URL = #
REACT_APP_RELEASE = AU
REACT_APP_API_BASE_URL_FANTASY = https://staging.smartb.au.sydney.digiground.com.au/fantasy-api/fantasy
REACT_APP_MEDIA_URL = https://media.staging.smartb.au.sydney.digiground.com.au/
```

## Environment Variables

The following environment variables are used in this project. Set them in a `.env` file or your deployment environment:

```env
REACT_APP_API_BASE_URL
REACT_APP_BASE_NAME
REACT_APP_BASE_URL
REACT_APP_WP_BASE_URL
REACT_APP_RELEASE
REACT_APP_API_BASE_URL_FANTASY
REACT_APP_MEDIA_URL
REACT_APP_API_Individual_URL
REACT_APP_BUILD_ENV
PUBLIC_URL
NODE_ENV
```

## Available Scripts

In the project directory, you can run:

```bash
npm start
```

Runs the app in development mode.
Open [http://localhost:3000](http://localhost:3000) to view it in the browser.

```bash
npm run build
```

Builds the app for production to the `build` folder.

## Testing

This project uses **Jest** and **React Testing Library** for unit and integration tests.

To run tests:

```bash
npm test
```

- Place your test files alongside components or in a `__tests__` folder.
- Test files should have the `.test.js` or `.test.jsx` extension.

## Deployment

### Docker

To build and run the app using Docker:

```bash
docker build -t smartb-webapp-admin .
docker run -p 3000:3000 smartb-webapp-admin
```

- The Dockerfile is set up for production builds.
- You may need to set environment variables at runtime (see above).

### Jenkins/CI

- The project includes a `Jenkinsfile` for CI/CD automation.
- The pipeline builds and pushes a Docker image, then deploys using `docker-compose`.
- Make sure to configure your Jenkins environment variables and secrets as needed.

#### Manual Deployment

- You can also deploy the build output (`npm run build`) to any static hosting service (e.g., Netlify, Vercel, S3, etc.).

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/YourFeature`)
3. Commit your changes (`git commit -am 'Add some feature'`)
4. Push to the branch (`git push origin feature/YourFeature`)
5. Create a new Pull Request

## Contact

- Repo owner: [Your Name] (<EMAIL>)
- For issues, please use the issue tracker.

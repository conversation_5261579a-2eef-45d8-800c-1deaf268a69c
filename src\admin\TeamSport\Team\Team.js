import React from "react";
import {
  Grid,
  Paper,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Modal,
  Box,
  Breadcrumbs,
  Typography,
  Button,
  TextField,
  InputAdornment,
  FormControl,
  RadioGroup,
  FormControlLabel,
  Radio,
  IconButton,
} from "@mui/material";
import styled from "styled-components";
import { Link } from "react-router-dom";
import CancelIcon from "@mui/icons-material/Cancel";
import SearchIcons from "../../../images/searchIcon.svg";
import axiosInstance from "../../../helpers/Axios";
import { URLS } from "../../../library/common/constants";
import ActionMessage from "../../../library/common/components/ActionMessage";
import { Loader } from "../../../library/common/components";
import ShowModal from "../../../components/common/ShowModal/ShowModal";
import ButtonComponent from "../../../library/common/components/Button";
import FileUploader from "../../../library/common/components/FileUploader";
import { config } from "../../../helpers/config";
import Select from "react-select";
import _ from "lodash";
import Pagination from "@mui/material/Pagination";
// import arrowLeft from "../../images/blog_img/paginationArrowLeft.svg";
// import arrowRight from "../../images/blog_img/paginationArrowRight.svg";
// import { ReactSVG } from "react-svg";
// import { showVariations } from "../../helpers/common";

import "../teamsport.scss";
import CreateTeamVariation from "./createTeamVariation/CreateTeamVariation";

const nationalData = [
  {
    label: "Yes",
    value: true,
  },
  {
    label: "No",
    value: false,
  },
];
class Team extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      isModalOpen: false,
      isInputModalOpen: false,
      isMergeModalOpen: false,
      isLoading: false,
      itemToDelete: null,
      idToSend: null,
      isEditMode: false,
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      currentPage: 1,
      rowPerPage: 20,
      offset: 0,
      teamValues: {
        teamName: "",
        national: "",
        gender: "",
        rapidTeamId: "",
        CountryId: "",
        id: "",
        CountryName: "",
        parentTeam: "",
        childTeam: [],
      },
      countryAll: [],
      countryCount: 0,
      pageCountry: 0,
      searchCountry: [],
      searchCountryCount: 0,
      SearchCountrypage: 0,
      isCountrySearch: "",
      TeamList: [],
      TeamCount: 0,
      errorName: "",
      errorNational: "",
      errorGender: "",
      errorCountry: "",
      flag: [],
      uploadLogo: "",
      createError: "",
      isVariationModalOpen: false,
      search: "",
      searchTeam: [],
      searchTeamCount: 0,
      searchTeamPage: 0,
      isTeamSearch: "",
      ModalTeamCount: 0,
      searchModalTeam: [],
      searchModalTeamCount: 0,
      searchModalTeamPage: 0,
      isModalTeamSearch: "",
      ModalTeamData: [],
      ModalTeamPage: 0,
      ModalChildTeamCount: 0,
      searchModalChildTeam: [],
      searchModalChildTeamCount: 0,
      searchModalChildTeamPage: 0,
      isModalChildTeamSearch: "",
      ModalChildTeamData: [],
      ModalChildTeamPage: 0,
      ModalChildTeamSearch: "",
      ModalParentTeamSearch: "",
    };
  }

  componentDidMount() {
    this.fetchAllTeam(0, "");
    // this.fetchAllCountry(0);
  }
  componentDidUpdate(prevProps, prevState) {
    if (prevState.offset !== this.state.offset) {
      this.fetchAllTeam(this.state.offset, this.state?.search);
    }
    if (prevProps.match.path !== this.props.match.path) {
      this.fetchAllTeam(0, "");
      this.setState({
        offset: 0,
        selectCategory: "",
        currentPage: 1,
        search: "",
      });
    }
  }
  fetchAllCountry = async (page) => {
    const { status, data } = await axiosInstance.get(
      // `${URLS.distance}?size=20&page=${page}`
      `${URLS.country}?limit=20&offset=${page}`
    );

    if (status === 200) {
      let Parray = [...this.state.countryAll];

      let count = data?.result?.count / 20;
      let newdata = [];
      let track = data?.result?.rows?.map((item) => {
        newdata.push({
          label: item?.country,
          value: item?.id,
        });
      });
      let filterData = _.unionBy(this.state.countryAll, newdata);

      this.setState({
        countryAll: _.uniqBy(filterData, function (e) {
          return e.value;
        }),
        countryCount: Math.ceil(count),
      });
    }
  };

  handleOnScrollBottomCountry = () => {
    let {
      pageCountry,
      isCountrySearch,
      searchCountryCount,
      SearchCountrypage,
      countryCount,
    } = this.state;

    if (
      isCountrySearch !== "" &&
      searchCountryCount !== Math.ceil(SearchCountrypage / 20 + 1)
    ) {
      this.handleCountryInputChange(SearchCountrypage + 20, isCountrySearch);
      this.setState({
        SearchCountrypage: SearchCountrypage + 20,
      });
    } else {
      if (countryCount !== Math.ceil(pageCountry / 20)) {
        this.fetchAllCountry(pageCountry + 20);
        this.setState({
          pageCountry: pageCountry + 20,
        });
      }
    }
  };

  handleCountryInputChange = (page, value) => {
    // if (value.length > 2) {
    axiosInstance
      .get(`${URLS.country}?limit=20&offset=${page}&search=${value}`)
      .then((res) => {
        if (res.status === 200) {
          let response = res?.data?.result?.rows;
          //     let Parray = [...this.state.countryAll];
          let count = res?.data?.result?.count / 20;

          let newdata = [];
          let FinalData = response?.map((item) => {
            newdata.push({
              label: item?.country,
              value: item?.id,
            });
          });
          let filterData = _.unionBy(this.state.searchCountry, newdata);

          this.setState({
            searchCountry: _.uniqBy(filterData, function (e) {
              return e.value;
            }),
            searchCountryCount: Math.ceil(count),
            isCountrySearch: value,
          });
        }
      });
  };
  fetchSelectedCountry = (CountryId, CountryName) => {
    let seletedCountry = [
      {
        label: CountryName,
        value: CountryId,
      },
    ];

    this.setState({
      countryAll: CountryId ? seletedCountry : this.state.countryAll,
    });
  };
  fetchAllTeam = async (page, searchvalue) => {
    let { rowPerPage, offset } = this.state;
    this.setState({ isLoading: true });
    try {
      const passApi = this.props.match.path?.includes("cricket")
        ? `crickets/team?limit=${rowPerPage}&offset=${page}&search=${searchvalue}`
        : this.props.match.path?.includes("rugbyleague")
        ? `rls/team?limit=${rowPerPage}&offset=${page}&SportId=12&search=${searchvalue}`
        : this.props.match.path?.includes("rugbyunion")
        ? `rls/team?limit=${rowPerPage}&offset=${page}&SportId=13&search=${searchvalue}`
        : this.props.match.path?.includes("basketball")
        ? `nba/team?limit=${rowPerPage}&offset=${page}&search=${searchvalue}`
        : this.props.match.path?.includes("afl")
        ? `afl/team?limit=${rowPerPage}&offset=${page}&search=${searchvalue}`
        : this.props.match.path?.includes("australianrules")
        ? `ar/team?limit=${rowPerPage}&offset=${page}&search=${searchvalue}`
        : this.props.match.path?.includes("golf")
        ? `golf/team?limit=${rowPerPage}&offset=${page}&search=${searchvalue}`
        : this.props.match.path?.includes("tennis")
        ? `tennis/team?limit=${rowPerPage}&offset=${page}&search=${searchvalue}`
        : this.props.match.path?.includes("baseball")
        ? `baseball/team?limit=${rowPerPage}&offset=${page}&search=${searchvalue}`
        : this.props.match.path?.includes("icehockey")
        ? `icehockey/team?limit=${rowPerPage}&offset=${page}&search=${searchvalue}`
        : this.props.match.path?.includes("boxing")
        ? `boxing/team?limit=${rowPerPage}&offset=${page}&search=${searchvalue}`
        : this.props.match.path?.includes("mma")
        ? `mma/team?limit=${rowPerPage}&offset=${page}&search=${searchvalue}`
        : this.props.match.path?.includes("soccer")
        ? `soccer/team?limit=${rowPerPage}&offset=${page}&search=${searchvalue}`
        : `rls/team?limit=${rowPerPage}&offset=${page}&SportId=14`;
      const { status, data } = await axiosInstance.get(passApi);
      if (status === 200) {
        this.setState({
          TeamList: data?.result?.rows,
          isLoading: false,
          TeamCount: data?.result?.count,
        });
      } else {
        this.setState({
          isLoading: false,
        });
      }
    } catch {
      this.setState({
        isLoading: false,
      });
    }
  };

  handalValidate = () => {
    let { teamValues } = this.state;

    let flag = true;
    if (teamValues?.teamName?.trim() === "") {
      flag = false;
      this.setState({
        errorName: "This field is mandatory",
      });
    } else {
      this.setState({
        errorName: "",
      });
    }
    if (teamValues?.national === "") {
      flag = false;
      this.setState({
        errorNational: "This field is mandatory",
      });
    } else {
      this.setState({
        errorNational: "",
      });
    }
    if (teamValues?.gender === "") {
      flag = false;
      this.setState({
        errorGender: "This field is mandatory",
      });
    } else {
      this.setState({
        errorGender: "",
      });
    }
    if (teamValues?.CountryId === "" || teamValues?.CountryId === null) {
      flag = false;
      this.setState({
        errorCountry: "This field is mandatory",
      });
    } else {
      this.setState({
        errorCountry: "",
      });
    }

    return flag;
  };
  handleSave = async () => {
    const { flag } = this.state;
    if (this.handalValidate()) {
      this.setState({ isLoading: true, isEditMode: false });

      let payload = {
        name: this.state?.teamValues?.teamName.trim(),
        rapidTeamId: this.state?.teamValues?.rapidTeamId,
        national: this.state?.teamValues?.national,
        gender: this.state?.teamValues?.gender,
        SportId: this.props.match.path?.includes("cricket")
          ? 4
          : this.props.match.path?.includes("rugbyleague")
          ? 12
          : this.props.match.path?.includes("rugbyunion")
          ? 13
          : this.props.match.path?.includes("basketball")
          ? 10
          : this.props.match.path?.includes("afl")
          ? 15
          : this.props.match.path?.includes("australianrules")
          ? 9
          : this.props.match.path?.includes("golf")
          ? 16
          : this.props.match.path?.includes("tennis")
          ? 7
          : this.props.match.path?.includes("baseball")
          ? 11
          : this.props.match.path?.includes("icehockey")
          ? 17
          : this.props.match.path?.includes("boxing")
          ? 6
          : this.props.match.path?.includes("mma")
          ? 5
          : this.props.match.path?.includes("soccer")
          ? 8
          : 14,
        CountryId: this.state?.teamValues?.CountryId,
      };
      if (flag?.length > 0) {
        let fileData = await this.setMedia(flag[0]);
        if (fileData) {
          payload = {
            ...payload,
            flag: fileData?.image?.filePath,
          };
          this.setState({
            uploadLogo: fileData?.image?.filePath,
          });
        }
      }
      let passApi = this.props.match.path?.includes("cricket")
        ? "crickets"
        : this.props.match.path?.includes("basketball")
        ? "nba"
        : this.props.match.path?.includes("afl")
        ? "afl"
        : this.props.match.path?.includes("australianrules")
        ? "ar"
        : this.props.match.path?.includes("golf")
        ? "golf"
        : this.props.match.path?.includes("tennis")
        ? "tennis"
        : this.props.match.path?.includes("baseball")
        ? "baseball"
        : this.props.match.path?.includes("icehockey")
        ? "icehockey"
        : this.props.match.path?.includes("boxing")
        ? "boxing"
        : this.props.match.path?.includes("mma")
        ? "mma"
        : this.props.match.path?.includes("soccer")
        ? "soccer"
        : "rls";
      try {
        const { status, data } = await axiosInstance.post(
          `${passApi}/team`,
          payload
        );
        if (status === 200) {
          this.setState({
            isLoading: false,
            isInputModalOpen: false,
            flag: [],
            uploadLogo: "",
          });
          this.fetchAllTeam(this.state.offset, this.state?.search);
          this.setActionMessage(
            true,
            "Success",
            data?.status ? "Team created Successfully..." : ""
          );
        } else {
          this.setActionMessage(true, "Error", data?.message);

          this.setState({ isLoading: false, createError: data?.message });
        }
      } catch (err) {
        this.setActionMessage(true, "Error", err?.response?.data?.message);
        this.setState({
          isLoading: false,
          createError: err?.response?.data?.message,
        });
      }
    }
  };
  handleUpdate = async () => {
    const { flag } = this.state;
    if (this.handalValidate()) {
      this.setState({ isLoading: true, isEditMode: true });

      let payload = {
        name: this.state?.teamValues?.teamName.trim(),
        rapidTeamId: this.state?.teamValues?.rapidTeamId,
        national: this.state?.teamValues?.national,
        gender: this.state?.teamValues?.gender,
        SportId: this.props.match.path?.includes("cricket")
          ? 4
          : this.props.match.path?.includes("rugbyleague")
          ? 12
          : this.props.match.path?.includes("rugbyunion")
          ? 13
          : this.props.match.path?.includes("basketball")
          ? 10
          : this.props.match.path?.includes("afl")
          ? 15
          : this.props.match.path?.includes("australianrules")
          ? 9
          : this.props.match.path?.includes("golf")
          ? 16
          : this.props.match.path?.includes("tennis")
          ? 7
          : this.props.match.path?.includes("baseball")
          ? 11
          : this.props.match.path?.includes("icehockey")
          ? 17
          : this.props.match.path?.includes("boxing")
          ? 6
          : this.props.match.path?.includes("mma")
          ? 5
          : this.props.match.path?.includes("soccer")
          ? 8
          : 14,
        CountryId: this.state?.teamValues?.CountryId,
      };
      if (flag?.length > 0) {
        let fileData = await this.setMedia(flag[0]);
        if (fileData) {
          payload = {
            ...payload,
            flag: fileData?.image?.filePath,
          };
          this.setState({
            uploadLogo: fileData?.image?.filePath,
          });
        }
      } else {
        payload = {
          ...payload,
          flag: this.state.uploadLogo,
        };
      }
      let passApi = this.props.match.path?.includes("cricket")
        ? "crickets"
        : this.props.match.path?.includes("basketball")
        ? "nba"
        : this.props.match.path?.includes("afl")
        ? "afl"
        : this.props.match.path?.includes("australianrules")
        ? "ar"
        : this.props.match.path?.includes("golf")
        ? "golf"
        : this.props.match.path?.includes("tennis")
        ? "tennis"
        : this.props.match.path?.includes("baseball")
        ? "baseball"
        : this.props.match.path?.includes("icehockey")
        ? "icehockey"
        : this.props.match.path?.includes("boxing")
        ? "boxing"
        : this.props.match.path?.includes("mma")
        ? "mma"
        : this.props.match.path?.includes("soccer")
        ? "soccer"
        : "rls";
      try {
        const { status, data } = await axiosInstance.put(
          `${passApi}/team/${this.state.teamValues?.id}`,
          payload
        );
        if (status === 200) {
          this.setState({
            isLoading: false,
            isInputModalOpen: false,
            flag: [],
            uploadLogo: "",
          });
          this.fetchAllTeam(this.state.offset, this.state?.search);
          this.setActionMessage(
            true,
            "Success",
            data?.status ? "Team Updated Successfully..." : ""
          );
        } else {
          this.setActionMessage(true, "Error", data?.message);

          this.setState({ isLoading: false, createError: data?.message });
        }
      } catch (err) {
        this.setActionMessage(true, "Error", err?.response?.data?.message);
        this.setState({
          isLoading: false,
          createError: err?.response?.data?.message,
        });
      }
    }
  };

  toggleModal = () => {
    this.setState({
      isModalOpen: !this.state.isModalOpen,
      itemToDelete: null,
    });
  };

  toggleInputModal = () => {
    this.setState({
      isInputModalOpen: !this.state.isInputModalOpen,
      errorName: "",
      errorGender: "",
      errorNational: "",
      errorCountry: "",
      flag: [],
      uploadLogo: "",
      createError: "",
    });
  };

  inputModal = (item, type) => () => {
    this.fetchAllCountry(0);
    this.setState({ isInputModalOpen: true });
    if (type === "edit") {
      this.fetchSelectedCountry(item?.CountryId, item?.Country?.country);
      this.setState({
        teamValues: {
          teamName: item?.name,
          national: item?.national,
          gender: item?.gender,
          rapidTeamId: item?.rapidTeamId,
          CountryId: item?.CountryId,
          CountryName: item?.Country?.country,
          id: item?.id,
        },
        uploadLogo: item?.flag,
        isEditMode: true,
      });
    } else {
      this.setState({
        teamValues: {
          teamName: "",
          national: "",
          gender: "",
          rapidTeamId: "",
          CountryId: null,
          CountryName: "",
          id: item?.id,
        },
        flag: [],
        uploadLogo: "",
        isEditMode: false,
      });
    }
  };

  mergeModal = (item) => () => {
    this.setState({
      isMergeModalOpen: true,
    });
    this.fetchModalParentTeam(0, "");
    this.fetchModalChildTeam(0, "");
    let teams = "";
    teams = {
      value: item?.id,
      label: item?.name,
    };
    this.setState({
      teamValues: {
        parentTeam: teams,
        childTeam: [],
      },
    });
  };

  toggleMergeModal = () => {
    this.setState({
      isMergeModalOpen: false,
      createError: "",
      teamValues: {
        parentTeam: "",
        childTeam: [],
      },
    });
  };

  setActionMessage = (display = false, type = "", message = "") => {
    this.setState({ messageBox: { display, type, message } }, () =>
      setTimeout(
        () =>
          this.setState({
            messageBox: { display: false, type: "", message: "" },
          }),
        3000
      )
    );
  };

  setItemToDelete = (id) => () => {
    this.setState({ itemToDelete: id, isModalOpen: true });
  };

  afterChangeRefresh = () => {
    // this.fetchAllSports();
  };

  deleteItem = async () => {
    try {
      const passApi = this.props.match.path?.includes("cricket")
        ? `crickets/team/${this.state.itemToDelete}`
        : this.props.match.path?.includes("rugbyleague")
        ? `rls/team/${this.state.itemToDelete}?SportId=12`
        : this.props.match.path?.includes("rugbyunion")
        ? `rls/team/${this.state.itemToDelete}?SportId=13`
        : this.props.match.path?.includes("basketball")
        ? `nba/team/${this.state.itemToDelete}`
        : this.props.match.path?.includes("afl")
        ? `afl/team/${this.state.itemToDelete}`
        : this.props.match.path?.includes("australianrules")
        ? `ar/team/${this.state.itemToDelete}`
        : this.props.match.path?.includes("golf")
        ? `golf/team/${this.state.itemToDelete}`
        : this.props.match.path?.includes("tennis")
        ? `tennis/team/${this.state.itemToDelete}`
        : this.props.match.path?.includes("baseball")
        ? `baseball/team/${this.state.itemToDelete}`
        : this.props.match.path?.includes("icehockey")
        ? `icehockey/team/${this.state.itemToDelete}`
        : this.props.match.path?.includes("boxing")
        ? `boxing/team/${this.state.itemToDelete}`
        : this.props.match.path?.includes("mma")
        ? `mma/team/${this.state.itemToDelete}`
        : this.props.match.path?.includes("soccer")
        ? `soccer/team/${this.state.itemToDelete}`
        : `rls/team/${this.state.itemToDelete}?SportId=14`;
      const { status } = await axiosInstance.delete(passApi);
      if (status === 200) {
        this.setState({ itemToDelete: null, isModalOpen: false }, () => {
          this.fetchAllTeam(this.state.offset, this.state?.search);
        });
        // this.setActionMessage(true, "Success", "Sport Deleted Successfully!");
      }
    } catch (err) {
      // this.setActionMessage(true, "Error", "An error occurred while deleting.");
    }
  };

  handlePaginationClick = (event, page) => {
    let { rowPerPage } = this.state;
    this.setState({
      currentPage: Number(page),
      offset: (Number(page) - 1) * rowPerPage,
    });
  };

  handlePaginationButtonClick = (navDirection) => {
    let { currentPage, offset } = this.state;
    if (navDirection === "prev") {
      if (offset >= 20) {
        this.setState({ offset: offset - 20, currentPage: currentPage - 1 });
      }
    } else {
      this.setState({ offset: offset + 20, currentPage: currentPage + 1 });
    }
  };
  handleFileUpload = (name, files) => {
    this.setState({ [name]: files });
  };
  setMedia = async (files) => {
    const formData = new FormData();
    formData.append("image", files ? files : null);
    if (files !== undefined) {
      const { status, data } = await axiosInstance.post(URLS.media, formData, {
        header: { "Content-Type": "multipart/form-data" },
      });
      if (status === 200) {
        return data;
      } else {
        return false;
      }
    }
    return false;
  };

  inputVariationModal = (item) => {
    this.setState({
      isVariationModalOpen: true,
      teamValues: {
        teamName: item?.name,
        id: item?.id,
      },
    });
  };
  toggleVariationModal = () => {
    this.setState({
      isVariationModalOpen: false,
      teamValues: {
        teamName: "",
        id: "",
      },
    });
  };

  fetchModalParentTeam = async (ModalTeamPage, searchvalue) => {
    const passApi = this.props.match.path?.includes("cricket")
      ? `crickets/team?limit=20&offset=${ModalTeamPage}&search=${searchvalue}`
      : this.props.match.path?.includes("rugbyleague")
      ? `rls/team?limit=20&offset=${ModalTeamPage}&search=${searchvalue}&SportId=12`
      : this.props.match.path?.includes("rugbyunion")
      ? `rls/team?limit=20&offset=${ModalTeamPage}&search=${searchvalue}&SportId=13`
      : this.props.match.path?.includes("basketball")
      ? `nba/team?limit=20&offset=${ModalTeamPage}&search=${searchvalue}`
      : this.props.match.path?.includes("afl")
      ? `afl/team?limit=20&offset=${ModalTeamPage}&search=${searchvalue}`
      : this.props.match.path?.includes("australianrules")
      ? `ar/team?limit=20&offset=${ModalTeamPage}&search=${searchvalue}`
      : this.props.match.path?.includes("golf")
      ? `golf/team?limit=20&offset=${ModalTeamPage}&search=${searchvalue}`
      : this.props.match.path?.includes("tennis")
      ? `tennis/team?limit=20&offset=${ModalTeamPage}&search=${searchvalue}`
      : this.props.match.path?.includes("baseball")
      ? `baseball/team?limit=20&offset=${ModalTeamPage}&search=${searchvalue}`
      : this.props.match.path?.includes("icehockey")
      ? `icehockey/team?limit=20&offset=${ModalTeamPage}&search=${searchvalue}`
      : this.props.match.path?.includes("boxing")
      ? `boxing/team?limit=20&offset=${ModalTeamPage}&search=${searchvalue}`
      : this.props.match.path?.includes("mma")
      ? `mma/team?limit=20&offset=${ModalTeamPage}&search=${searchvalue}`
      : this.props.match.path?.includes("soccer")
      ? `soccer/team?limit=20&offset=${ModalTeamPage}&search=${searchvalue}`
      : `rls/team?limit=20&offset=${ModalTeamPage}&search=${searchvalue}&SportId=14`;
    const { status, data } = await axiosInstance.get(passApi);
    if (status === 200) {
      let count = data?.result?.count / 20;
      let newdata = [];
      let categories = data?.result?.rows?.map((item) => {
        newdata.push({
          label: item?.name,
          value: item?.id,
        });
      });
      let filterData = _.unionBy(this.state?.ModalTeamData, newdata);
      const sortedData = filterData?.sort((a, b) => {
        return a?.label.localeCompare(b?.label);
      });
      let finalData = _.uniqBy(sortedData, function (e) {
        return e.value;
      });

      this.setState({
        ModalTeamData: finalData,
        ModalTeamCount: Math.ceil(count),
      });
    }
  };

  handleOnScrollBottomModalParentTeam = (e, type) => {
    let {
      ModalTeamCount,
      ModalTeamPage,
      isModalTeamSearch,
      searchModalTeamCount,
      searchModalTeamPage,
    } = this.state;
    if (
      isModalTeamSearch !== "" &&
      searchModalTeamCount !== Math.ceil(searchModalTeamPage / 20 + 1)
    ) {
      this.handleModalParentTeamInputChange(
        searchModalTeamPage + 20,
        isModalTeamSearch
      );
      this.setState({
        searchModalTeamPage: searchModalTeamPage + 20,
      });
    } else {
      if (
        ModalTeamCount !==
          (ModalTeamCount == 1 ? 1 : Math.ceil(ModalTeamPage / 20)) &&
        isModalTeamSearch == ""
      ) {
        this.fetchModalParentTeam(ModalTeamPage + 20, isModalTeamSearch);
        this.setState({
          ModalTeamPage: ModalTeamPage + 20,
        });
      }
    }
  };
  handleModalParentTeamInputChange = (ModalTeamPage, value) => {
    const passApi = this.props.match.path?.includes("cricket")
      ? `crickets/team?limit=20&offset=${ModalTeamPage}&search=${value}`
      : this.props.match.path?.includes("rugbyleague")
      ? `rls/team?limit=20&offset=${ModalTeamPage}&search=${value}&SportId=12`
      : this.props.match.path?.includes("rugbyunion")
      ? `rls/team?limit=20&offset=${ModalTeamPage}&search=${value}&SportId=13`
      : this.props.match.path?.includes("basketball")
      ? `nba/team?limit=20&offset=${ModalTeamPage}&search=${value}`
      : this.props.match.path?.includes("afl")
      ? `afl/team?limit=20&offset=${ModalTeamPage}&search=${value}`
      : this.props.match.path?.includes("australianrules")
      ? `ar/team?limit=20&offset=${ModalTeamPage}&search=${value}`
      : this.props.match.path?.includes("golf")
      ? `golf/team?limit=20&offset=${ModalTeamPage}&search=${value}`
      : this.props.match.path?.includes("tennis")
      ? `tennis/team?limit=20&offset=${ModalTeamPage}&search=${value}`
      : this.props.match.path?.includes("baseball")
      ? `baseball/team?limit=20&offset=${ModalTeamPage}&search=${value}`
      : this.props.match.path?.includes("icehockey")
      ? `icehockey/team?limit=20&offset=${ModalTeamPage}&search=${value}`
      : this.props.match.path?.includes("boxing")
      ? `boxing/team?limit=20&offset=${ModalTeamPage}&search=${value}`
      : this.props.match.path?.includes("mma")
      ? `mma/team?limit=20&offset=${ModalTeamPage}&search=${value}`
      : this.props.match.path?.includes("soccer")
      ? `soccer/team?limit=20&offset=${ModalTeamPage}&search=${value}`
      : `rls/team?limit=20&offset=${ModalTeamPage}&search=${value}&SportId=14`;
    axiosInstance.get(passApi).then((data) => {
      if (data?.status === 200) {
        let count = data?.data?.result?.count / 20;
        let newdata = [];
        let categories = data?.data?.result?.rows?.map((item) => {
          newdata.push({
            label: item?.name,
            value: item?.id,
          });
        });
        let filterData = _.unionBy(this.state?.searchModalTeam, newdata);
        const sortedData = filterData?.sort((a, b) => {
          return a?.label.localeCompare(b?.label);
        });
        let finalData = _.uniqBy(sortedData, function (e) {
          return e.value;
        });
        this.setState({
          searchModalTeam: finalData,
          searchModalTeamCount: Math.ceil(count),
          isModalTeamSearch: value,
        });
      }
    });
  };

  fetchModalChildTeam = async (ModalTeamPage, searchvalue) => {
    const passApi = this.props.match.path?.includes("cricket")
      ? `crickets/team?limit=20&offset=${ModalTeamPage}&search=${searchvalue}`
      : this.props.match.path?.includes("rugbyleague")
      ? `rls/team?limit=20&offset=${ModalTeamPage}&search=${searchvalue}&SportId=12`
      : this.props.match.path?.includes("rugbyunion")
      ? `rls/team?limit=20&offset=${ModalTeamPage}&search=${searchvalue}&SportId=13`
      : this.props.match.path?.includes("basketball")
      ? `nba/team?limit=20&offset=${ModalTeamPage}&search=${searchvalue}`
      : this.props.match.path?.includes("afl")
      ? `afl/team?limit=20&offset=${ModalTeamPage}&search=${searchvalue}`
      : this.props.match.path?.includes("australianrules")
      ? `ar/team?limit=20&offset=${ModalTeamPage}&search=${searchvalue}`
      : this.props.match.path?.includes("golf")
      ? `golf/team?limit=20&offset=${ModalTeamPage}&search=${searchvalue}`
      : this.props.match.path?.includes("tennis")
      ? `tennis/team?limit=20&offset=${ModalTeamPage}&search=${searchvalue}`
      : this.props.match.path?.includes("baseball")
      ? `baseball/team?limit=20&offset=${ModalTeamPage}&search=${searchvalue}`
      : this.props.match.path?.includes("icehockey")
      ? `icehockey/team?limit=20&offset=${ModalTeamPage}&search=${searchvalue}`
      : this.props.match.path?.includes("boxing")
      ? `boxing/team?limit=20&offset=${ModalTeamPage}&search=${searchvalue}`
      : this.props.match.path?.includes("mma")
      ? `mma/team?limit=20&offset=${ModalTeamPage}&search=${searchvalue}`
      : this.props.match.path?.includes("soccer")
      ? `soccer/team?limit=20&offset=${ModalTeamPage}&search=${searchvalue}`
      : `rls/team?limit=20&offset=${ModalTeamPage}&search=${searchvalue}&SportId=14`;
    const { status, data } = await axiosInstance.get(passApi);
    if (status === 200) {
      let count = data?.result?.count / 20;
      let newdata = [];
      let categories = data?.result?.rows?.map((item) => {
        newdata.push({
          label: item?.name,
          value: item?.id,
        });
      });
      let filterData = _.unionBy(this.state?.ModalChildTeamData, newdata);
      const sortedData = filterData?.sort((a, b) => {
        return a?.label.localeCompare(b?.label);
      });
      let finalData = _.uniqBy(sortedData, function (e) {
        return e.value;
      }).filter(
        (team) => team?.value !== this.state.teamValues?.parentTeam?.value
      );

      this.setState({
        ModalChildTeamData: finalData,
        ModalChildTeamCount: Math.ceil(count),
      });
    }
  };

  handleOnScrollBottomModalChildTeam = (e, type) => {
    let {
      ModalChildTeamCount,
      ModalChildTeamPage,
      isModalChildTeamSearch,
      searchModalChildTeamCount,
      searchModalChildTeamPage,
    } = this.state;
    if (
      isModalChildTeamSearch !== "" &&
      searchModalChildTeamCount !== Math.ceil(searchModalChildTeamPage / 20 + 1)
    ) {
      this.handleModalChildTeamInputChange(
        searchModalChildTeamPage + 20,
        isModalChildTeamSearch
      );
      this.setState({
        searchModalChildTeamPage: searchModalChildTeamPage + 20,
      });
    } else {
      if (
        ModalChildTeamCount !==
          (ModalChildTeamCount == 1 ? 1 : Math.ceil(ModalChildTeamPage / 20)) &&
        isModalChildTeamSearch == ""
      ) {
        this.fetchModalChildTeam(
          ModalChildTeamPage + 20,
          isModalChildTeamSearch
        );
        this.setState({
          ModalChildTeamPage: ModalChildTeamPage + 20,
        });
      }
    }
  };
  handleModalChildTeamInputChange = (ModalTeamPage, value) => {
    const passApi = this.props.match.path?.includes("cricket")
      ? `crickets/team?limit=20&offset=${ModalTeamPage}&search=${value}`
      : this.props.match.path?.includes("rugbyleague")
      ? `rls/team?limit=20&offset=${ModalTeamPage}&search=${value}&SportId=12`
      : this.props.match.path?.includes("rugbyunion")
      ? `rls/team?limit=20&offset=${ModalTeamPage}&search=${value}&SportId=13`
      : this.props.match.path?.includes("basketball")
      ? `nba/team?limit=20&offset=${ModalTeamPage}&search=${value}`
      : this.props.match.path?.includes("afl")
      ? `afl/team?limit=20&offset=${ModalTeamPage}&search=${value}`
      : this.props.match.path?.includes("australianrules")
      ? `ar/team?limit=20&offset=${ModalTeamPage}&search=${value}`
      : this.props.match.path?.includes("golf")
      ? `golf/team?limit=20&offset=${ModalTeamPage}&search=${value}`
      : this.props.match.path?.includes("tennis")
      ? `tennis/team?limit=20&offset=${ModalTeamPage}&search=${value}`
      : this.props.match.path?.includes("baseball")
      ? `baseball/team?limit=20&offset=${ModalTeamPage}&search=${value}`
      : this.props.match.path?.includes("icehockey")
      ? `icehockey/team?limit=20&offset=${ModalTeamPage}&search=${value}`
      : this.props.match.path?.includes("boxing")
      ? `boxing/team?limit=20&offset=${ModalTeamPage}&search=${value}`
      : this.props.match.path?.includes("mma")
      ? `mma/team?limit=20&offset=${ModalTeamPage}&search=${value}`
      : this.props.match.path?.includes("soccer")
      ? `soccer/team?limit=20&offset=${ModalTeamPage}&search=${value}`
      : `rls/team?limit=20&offset=${ModalTeamPage}&search=${value}&SportId=14`;
    axiosInstance.get(passApi).then((data) => {
      if (data?.status === 200) {
        let count = data?.data?.result?.count / 20;
        let newdata = [];
        let categories = data?.data?.result?.rows?.map((item) => {
          newdata.push({
            label: item?.name,
            value: item?.id,
          });
        });
        let filterData = _.unionBy(this.state?.searchModalTeam, newdata);
        const sortedData = filterData?.sort((a, b) => {
          return a?.label.localeCompare(b?.label);
        });
        let finalData = _.uniqBy(sortedData, function (e) {
          return e.value;
        })?.filter(
          (team) => team?.value !== this.state.teamValues?.parentTeam?.value
        );
        this.setState({
          searchModalChildTeam: finalData,
          searchModalChildTeamCount: Math.ceil(count),
          isModalChildTeamSearch: value,
        });
      }
    });
  };

  handalMergeTeam = async () => {
    this.setState({ isLoading: true });
    let payload = {
      parentTeamId: this.state?.teamValues?.parentTeam?.value,
      childTeamId: this.state?.teamValues?.childTeam?.map(
        (item) => item?.value
      ),
    };
    try {
      const passApi = this.props.match.path?.includes("cricket")
        ? `crickets/remove/duplicate`
        : this.props.match.path?.includes("rugbyleague")
        ? `rls/remove/duplicate?SportId=12`
        : this.props.match.path?.includes("rugbyunion")
        ? `rls/remove/duplicate?SportId=13`
        : this.props.match.path?.includes("basketball")
        ? `nba/remove/duplicate/`
        : this.props.match.path?.includes("afl")
        ? `afl/remove/duplicate/`
        : this.props.match.path?.includes("australianrules")
        ? `ar/remove/duplicate/`
        : this.props.match.path?.includes("golf")
        ? `golf/remove/duplicate/`
        : this.props.match.path?.includes("tennis")
        ? `tennis/remove/duplicate/`
        : this.props.match.path?.includes("baseball")
        ? `baseball/remove/duplicate/`
        : this.props.match.path?.includes("icehockey")
        ? `icehockey/remove/duplicate/`
        : this.props.match.path?.includes("boxing")
        ? `boxing/remove/duplicate/`
        : this.props.match.path?.includes("mma")
        ? `mma/remove/duplicate/`
        : this.props.match.path?.includes("soccer")
        ? `soccer/remove/duplicate/`
        : `rls/remove/duplicate?SportId=14`;
      const { status, data } = await axiosInstance.put(passApi, payload);
      if (status === 200) {
        this.setState({
          isLoading: false,
          isMergeModalOpen: false,
        });
        this.fetchAllTeam(this.state.offset, this.state?.search);
        this.setActionMessage(true, "Success", "Sport Deleted Successfully!");
      } else {
        this.setActionMessage(true, "Error", data?.message);

        this.setState({ isLoading: false, createError: data?.message });
      }
    } catch (err) {
      this.setActionMessage(true, "Error", err?.response?.data?.message);
      this.setState({
        isLoading: false,
        createError: err?.response?.data?.message,
      });
    }
  };

  handleClearClick = () => {
    this.setState({ search: "" });
  };

  handleKeyDown = (event) => {
    var { search } = this.state;
    if (event.key === "Enter") {
      this.fetchAllTeam(0, search);
      this.setState({ currentPage: 1 });
    }
  };

  render() {
    var {
      isModalOpen,
      messageBox,
      isInputModalOpen,
      isLoading,
      isEditMode,
      rowPerPage,
      currentPage,
      teamValues,
      countryAll,
      countryCount,
      pageCountry,
      searchCountry,
      searchCountryCount,
      SearchCountrypage,
      isCountrySearch,
      TeamList,
      TeamCount,
      errorName,
      errorNational,
      errorGender,
      errorCountry,
      flag,
      uploadLogo,
      createError,
      isVariationModalOpen,
      search,
      isMergeModalOpen,
      isModalTeamSearch,
      searchModalTeam,
      ModalTeamData,
      isModalChildTeamSearch,
      searchModalChildTeam,
      ModalChildTeamData,
      ModalChildTeamSearch,
      ModalParentTeamSearch,
    } = this.state;
    const pageNumbers = [];
    if (TeamCount > 0) {
      for (let i = 1; i <= Math.ceil(TeamCount / rowPerPage); i++) {
        pageNumbers.push(i);
      }
    }
    return (
      <>
        <Grid container className="page-content adminLogin">
          <Grid item xs={12} className="pageWrapper">
            {/* <Paper className="pageWrapper"> */}
            {messageBox.display && (
              <ActionMessage
                message={messageBox.message}
                type={messageBox.type}
                styleClass={messageBox.styleClass}
              />
            )}
            <Box className="bredcrumn-wrap">
              <Breadcrumbs
                separator="/"
                aria-label="breadcrumb"
                className="breadcrumb"
              >
                <Link underline="hover" color="inherit" to="/dashboard">
                  Home
                </Link>
                <Link underline="hover" color="inherit">
                  {this.props.match.path?.includes("cricket")
                    ? "Cricket"
                    : this.props.match.path?.includes("rugbyleague")
                    ? "Rugby League"
                    : this.props.match.path?.includes("rugbyunion")
                    ? "Rugby Union"
                    : this.props.match.path?.includes("basketball")
                    ? "Basketball"
                    : this.props.match.path?.includes("afl")
                    ? "American Football"
                    : this.props.match.path?.includes("australianrules")
                    ? "Australian Rules"
                    : this.props.match.path?.includes("golf")
                    ? "Golf"
                    : this.props.match.path?.includes("tennis")
                    ? "Tennis"
                    : this.props.match.path?.includes("baseball")
                    ? "Baseball"
                    : this.props.match.path?.includes("icehockey")
                    ? "Ice Hockey"
                    : this.props.match.path?.includes("boxing")
                    ? "Boxing"
                    : this.props.match.path?.includes("mma")
                    ? "Mixed Martial Arts"
                    : this.props.match.path?.includes("soccer")
                    ? "Soccer"
                    : "Rugby Union Sevens"}
                </Link>
                <Typography className="active_p">Team</Typography>
              </Breadcrumbs>
            </Box>
            <Grid container direction="row" alignItems="center">
              <Grid item xs={3}>
                <Typography variant="h1" align="left">
                  Team
                </Typography>
              </Grid>
              <Grid item xs={9} className="admin-filter-wrap">
                {/* <SelectBox
                onChange={(e) => this.setState({ sportType: e.target.value })}
                style={{ width: "20%", marginTop: "0px" }}
              >
                <option value="">Select Category</option>
                {CategoryData?.length > 0 &&
                  CategoryData?.map((obj, i) => (
                    <option key={i} value={obj?.id}>
                      {obj?.categoryName}
                    </option>
                  ))}
              </SelectBox> */}
                {/* <Select
                className="React teamsport-select"
                classNamePrefix="select"
                // onMenuScrollToBottom={(e) =>
                //   this.handleOnScrollBottomDistance(e)
                // }
                // onInputChange={(e) => this.handleDistanceInputChange(1, e)}
                // value={
                //   isDistanceSearch
                //     ? searchDistance?.find((item) => {
                //         return item?.value == distance;
                //       })
                //     : distanceAll?.find((item) => {
                //         return item?.value == distance;
                //       })
                // }
                // onChange={(e) =>
                //   this.setState({
                //     distance: e.value,
                //   })
                // }
                options={countryData}
              /> */}
                <TextField
                  placeholder="Search "
                  size="small"
                  variant="outlined"
                  className="txt-field-class"
                  value={search}
                  onKeyDown={(e) => this.handleKeyDown(e)}
                  onChange={(e) => {
                    this.setState({ search: e.target.value });
                  }}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <img src={SearchIcons} alt="icon" />
                      </InputAdornment>
                    ),
                    endAdornment: (
                      <InputAdornment position="end">
                        {search && (
                          <IconButton
                            onClick={() => this.handleClearClick()}
                            edge="end"
                            style={{ minWidth: "unset" }}
                            size="large"
                          >
                            <CancelIcon />
                          </IconButton>
                        )}
                      </InputAdornment>
                    ),
                  }}
                />
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455c7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                  }}
                  onClick={() => {
                    this.fetchAllTeam(0, search);
                    this.setState({ currentPage: 1 });
                  }}
                >
                  Search
                </Button>
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455C7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                    padding: "6px 10px",
                  }}
                  onClick={this.inputModal(null, "create")}
                >
                  Add New
                </Button>
              </Grid>
            </Grid>

            {isLoading && <Loader />}
            {!isLoading && TeamList?.length === 0 && <p>No Data Available</p>}

            {!isLoading && TeamList?.length > 0 && (
              <>
                <TableContainer component={Paper}>
                  <Table className="listTable" aria-label="simple table">
                    <TableHead className="tableHead-row">
                      <TableRow>
                        <TableCell>DID</TableCell>
                        <TableCell>rapidTeamId</TableCell>
                        <TableCell>Logo</TableCell>
                        <TableCell style={{ width: "25%" }}>
                          Team Name
                        </TableCell>
                        <TableCell>variation</TableCell>
                        <TableCell>National</TableCell>
                        <TableCell>Gender</TableCell>
                        <TableCell>Action</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody className="table_body">
                      <TableRow className="table_row">
                        <TableCell
                          colSpan={100}
                          className="table-seprator"
                        ></TableCell>
                      </TableRow>
                      {TeamList?.map((item) => {
                        return (
                          <TableRow className="table-rows listTable-Row">
                            <TableCell> {item?.id} </TableCell>
                            <TableCell>{item?.rapidTeamId}</TableCell>
                            <TableCell className="upload-img">
                              {item?.flag?.includes("uploads") ? (
                                <img
                                  src={config.mediaUrl + item?.flag}
                                  alt="teamlogo"
                                />
                              ) : item?.flag ? (
                                <img src={item?.flag} alt="teamlogo" />
                              ) : (
                                ""
                              )}
                            </TableCell>
                            <TableCell>{item?.name}</TableCell>
                            <TableCell>
                              <Button
                                variant="contained"
                                style={{
                                  backgroundColor: "#4455C7",
                                  color: "#fff",
                                  borderRadius: "8px",
                                  textTransform: "capitalize",
                                  // padding: "13px 24px 12px",
                                  marginLeft: "15px",
                                }}
                                onClick={() => {
                                  this.inputVariationModal(item);
                                }}
                              >
                                Add/Edit variation
                              </Button>
                            </TableCell>
                            <TableCell>
                              {item?.national === false ? "No" : "Yes"}
                            </TableCell>
                            <TableCell>{item?.gender}</TableCell>
                            <TableCell>
                              <Button
                                onClick={this.inputModal(item, "edit")}
                                style={{ cursor: "pointer" }}
                                className="table-btn edit-btn"
                              >
                                Edit
                              </Button>
                              <Button
                                onClick={this.setItemToDelete(item?.id)}
                                style={{ cursor: "pointer" }}
                                className="table-btn delete-btn"
                              >
                                Delete
                              </Button>
                              <Button
                                onClick={this.mergeModal(item)}
                                style={{ cursor: "pointer" }}
                                className="table-btn info-btn"
                              >
                                Merge
                              </Button>
                            </TableCell>
                          </TableRow>
                        );
                      })}
                      <TableRow>
                        <TableCell colSpan={100} className="pagination">
                          <div className="tablePagination">
                            {/* <button
                            className={
                              sports.length / rowPerPage > 1
                                ? "btn-navigation"
                                : "btn-navigation-disabled"
                            }
                            disabled={
                              sports.length / rowPerPage > 1 ? false : true
                            }
                            onClick={() =>
                              this.handlePaginationButtonClick("prev")
                            }
                          >
                            <ReactSVG src={arrowLeft} />
                          </button> */}
                            <Pagination
                              hideNextButton
                              hidePrevButton
                              disabled={
                                TeamCount / rowPerPage > 1 ? false : true
                              }
                              page={currentPage}
                              onChange={this.handlePaginationClick}
                              count={pageNumbers[pageNumbers?.length - 1]}
                              siblingCount={2}
                              boundaryCount={1}
                              size="small"
                            />
                            {/* <button
                            className={
                              sports.length / rowPerPage > 1
                                ? "btn-navigation"
                                : "btn-navigation-disabled"
                            }
                            disabled={
                              sports.length / rowPerPage > 1 ? false : true
                            }
                            onClick={() =>
                              this.handlePaginationButtonClick("next")
                            }
                          >
                            <ReactSVG src={arrowRight} />
                          </button> */}
                          </div>
                        </TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </TableContainer>
              </>
            )}
            {/* </Paper> */}

            <ShowModal
              isModalOpen={isModalOpen}
              onClose={this.toggleModal}
              Content="Are you sure, Events Assosiated with this team also be deleted ?"
              onOkayLabel="Yes"
              onOkay={this.deleteItem}
              onCancel={this.toggleModal}
            />

            <Modal
              className="modal modal-input"
              open={isInputModalOpen}
              onClose={this.toggleInputModal}
            >
              <div
                className={"paper modal-show-scroll"}
                style={{ position: "relative" }}
              >
                <h3 className="text-center">
                  {!isEditMode ? "Create New Team" : "Edit Team"}
                </h3>
                <CancelIcon
                  className="admin-close-icon"
                  onClick={this.toggleInputModal}
                />
                <Grid container className="page-content adminLogin text-left">
                  <Grid item xs={12} className="pageWrapper api-provider">
                    {/* <Paper className="pageWrapper api-provider"> */}
                    {/* {messageBox.display && (
                    <ActionMessage
                      message={messageBox.message}
                      type={messageBox.type}
                      styleClass={messageBox.styleClass}
                    />
                  )} */}
                    <Grid
                      item
                      xs={12}
                      style={{ display: "flex", flexDirection: "column" }}
                      className="teamsport-text"
                    >
                      <label className="modal-label"> Team Name</label>
                      <TextField
                        className="teamsport-textfield eventname"
                        variant="outlined"
                        color="primary"
                        size="small"
                        placeholder="Team Name"
                        value={teamValues?.teamName}
                        onChange={(e) =>
                          this.setState({
                            teamValues: {
                              ...teamValues,
                              teamName: e.target.value,
                            },
                          })
                        }
                      />
                      {errorName ? (
                        <p
                          className="errorText"
                          style={{ margin: "-14px 0 0 0" }}
                        >
                          {errorName}
                        </p>
                      ) : (
                        ""
                      )}
                    </Grid>
                    <Grid container>
                      {/* <Grid
                        item
                        xs={6}
                        style={{ display: "flex", flexDirection: "column" }}
                        className="teamsport-text"
                      >
                        <label className="modal-label">Short Team Name</label>
                        <TextField
                          className="teamsport-textfield"
                          variant="outlined"
                          color="primary"
                          size="small"
                          placeholder="Short Team Name"
                          value={teamValues?.shortTeamName}
                          onChange={(e) =>
                            this.setState({
                              teamValues: {
                                ...teamValues,
                                shortTeamName: e.target.value,
                              },
                            })
                          }
                        />
                      </Grid>
                      <Grid
                        item
                        xs={6}
                        style={{ display: "flex", flexDirection: "column" }}
                        className="teamsport-text"
                      >
                        <label className="modal-label">Abbreviation Name</label>
                        <TextField
                          className="teamsport-textfield"
                          variant="outlined"
                          color="primary"
                          size="small"
                          placeholder="Abbreviation Name"
                          value={teamValues?.abbreviation}
                          onChange={(e) =>
                            this.setState({
                              teamValues: {
                                ...teamValues,
                                abbreviation: e.target.value,
                              },
                            })
                          }
                        />
                      </Grid> */}
                      <Grid
                        item
                        xs={6}
                        style={{ display: "flex", flexDirection: "column" }}
                        className="teamsport-text"
                      >
                        <label className="modal-label">rapid Team Id</label>
                        <TextField
                          className="teamsport-textfield"
                          variant="outlined"
                          color="primary"
                          size="small"
                          placeholder="rapid Team Id"
                          value={teamValues?.rapidTeamId}
                          onChange={(e) =>
                            this.setState({
                              teamValues: {
                                ...teamValues,
                                rapidTeamId: e.target.value,
                              },
                            })
                          }
                        />
                      </Grid>
                      <Grid item xs={6} className="national-select">
                        <label className="modal-label"> Country </label>
                        <Select
                          className="React teamsport-select"
                          classNamePrefix="select"
                          menuPosition="fixed"
                          placeholder="Country"
                          onMenuScrollToBottom={(e) =>
                            this.handleOnScrollBottomCountry(e)
                          }
                          // isSearchable={false}
                          onInputChange={(e) =>
                            this.handleCountryInputChange(0, e)
                          }
                          value={
                            isCountrySearch
                              ? searchCountry?.find((item) => {
                                  return item?.value == teamValues?.CountryId;
                                })
                              : countryAll?.find((item) => {
                                  return item?.value == teamValues?.CountryId;
                                })
                          }
                          onChange={(e) =>
                            this.setState({
                              teamValues: {
                                ...teamValues,
                                CountryId: e.value,
                              },
                            })
                          }
                          options={isCountrySearch ? searchCountry : countryAll}
                        />
                        {errorCountry ? (
                          <p
                            className="errorText"
                            style={{ margin: "0px 0 0 0" }}
                          >
                            {errorCountry}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      <Grid item xs={6} className="national-select">
                        <label className="modal-label"> National </label>
                        <Select
                          className="React teamsport-select"
                          classNamePrefix="select"
                          menuPosition="fixed"
                          value={nationalData?.find((item) => {
                            return item?.value === teamValues?.national;
                          })}
                          onChange={(e) =>
                            this.setState({
                              teamValues: {
                                ...teamValues,
                                national: e.value,
                              },
                            })
                          }
                          options={nationalData}
                        />
                        {errorNational ? (
                          <p
                            className="errorText"
                            style={{ margin: "0px 0 0 0" }}
                          >
                            {errorNational}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      <Grid item xs={6} className="radio-wrap">
                        <FormControl component="fieldset">
                          <label className="modal-label"> Gender </label>
                          <RadioGroup
                            aria-label="gender"
                            name="gender"
                            className="gender"
                            value={teamValues?.gender}
                            onChange={(e) =>
                              this.setState({
                                teamValues: {
                                  ...teamValues,
                                  gender: e.target.value,
                                },
                              })
                            }
                          >
                            <FormControlLabel
                              value="F"
                              control={
                                <Radio
                                  color="primary"
                                  checked={teamValues?.gender === "F"}
                                />
                              }
                              label="Female"
                            />
                            <FormControlLabel
                              value="M"
                              control={
                                <Radio
                                  color="primary"
                                  checked={teamValues?.gender === "M"}
                                />
                              }
                              label="Male"
                            />
                          </RadioGroup>
                        </FormControl>
                        {errorGender ? (
                          <p
                            className="errorText"
                            style={{ margin: "0px 0 0 0" }}
                          >
                            {errorGender}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                    </Grid>
                    <div className="blog-file-upload">
                      <h6>Logo</h6>
                      <FileUploader
                        onDrop={(flag) => this.handleFileUpload("flag", flag)}
                      />
                      <div className="logocontainer">
                        {flag?.length > 0
                          ? flag?.map((file, index) => (
                              <img
                                className="auto-width"
                                key={index}
                                src={file.preview}
                                alt="teamlogo"
                              />
                            ))
                          : uploadLogo &&
                            uploadLogo !== "" && (
                              <img
                                className="auto-width"
                                src={config.mediaUrl + uploadLogo}
                                alt="teamlogo"
                              />
                            )}
                      </div>
                    </div>
                    <Grid container>
                      <Grid item xs={3}>
                        <div style={{ marginTop: "20px", display: "flex" }}>
                          {!isEditMode ? (
                            <ButtonComponent
                              className="mt-3 admin-btn-green"
                              onClick={this.handleSave}
                              color="primary"
                              value={!isLoading ? "Save" : "Loading..."}
                              disabled={isLoading}
                            />
                          ) : (
                            <ButtonComponent
                              className="mt-3 admin-btn-green"
                              onClick={this.handleUpdate}
                              color="secondary"
                              value={!isLoading ? "Update" : "Loading..."}
                              disabled={isLoading}
                            />
                          )}

                          <ButtonComponent
                            onClick={this.toggleInputModal}
                            className="mr-lr-30 back-btn-modal"
                            value="Back"
                          />
                        </div>
                        {createError ? (
                          <p
                            className="errorText"
                            style={{ margin: "0px 0 0 0", width: "300px" }}
                          >
                            {createError}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                    </Grid>
                    {/* </Paper> */}
                  </Grid>
                </Grid>
              </div>
            </Modal>
            <Modal
              className="modal modal-input"
              open={isVariationModalOpen}
              onClose={this.toggleVariationModal}
            >
              <div
                className={"paper modal-show-scroll"}
                style={{ position: "relative" }}
              >
                <h3 className="text-center modal-head">
                  {/* {isEditMode === true ? "Edit Track " : "Add Track"} */}
                  Variation Details
                </h3>
                <CancelIcon
                  className="admin-close-icon"
                  onClick={this.toggleVariationModal}
                />
                <CreateTeamVariation
                  inputModal={this.toggleVariationModal}
                  teamValues={teamValues}
                  pathName={this.props?.match?.path}
                />
              </div>
            </Modal>
            <Modal
              className="modal modal-input"
              open={isMergeModalOpen}
              onClose={this.toggleMergeModal}
            >
              <div
                className={"paper modal-show-scroll"}
                style={{ position: "relative" }}
              >
                <h3 className="text-center modal-head">
                  {/* {isEditMode === true ? "Edit Track " : "Add Track"} */}
                  Merge Team
                </h3>
                <CancelIcon
                  className="admin-close-icon"
                  onClick={this.toggleMergeModal}
                />
                <Grid container className="page-content adminLogin text-left">
                  <Grid item xs={12}>
                    <label className="modal-label"> Parent Teams </label>
                    <Select
                      className="React teamsport-select teamsport-multiple-select mb15"
                      classNamePrefix="select"
                      menuPosition="fixed"
                      onMenuScrollToBottom={(e) =>
                        this.handleOnScrollBottomModalParentTeam(e)
                      }
                      onInputChange={(e) =>
                        this.handleModalParentTeamInputChange(0, e)
                      }
                      value={
                        isModalTeamSearch
                          ? searchModalTeam?.find((item) => {
                              return item?.value == teamValues?.parentTeam;
                            })
                          : teamValues?.parentTeam
                      }
                      options={
                        isModalTeamSearch ? searchModalTeam : ModalTeamData
                      }
                      onChange={(e) =>
                        this.setState({
                          teamValues: {
                            ...teamValues,
                            parentTeam: e,
                          },
                        })
                      }
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <label className="modal-label"> Child Teams </label>
                    <Select
                      className="React teamsport-select teamsport-multiple-select"
                      classNamePrefix="select"
                      menuPosition="fixed"
                      isMulti
                      onMenuScrollToBottom={(e) =>
                        this.handleOnScrollBottomModalChildTeam(e)
                      }
                      onInputChange={(e) =>
                        this.handleModalChildTeamInputChange(0, e)
                      }
                      value={
                        isModalChildTeamSearch !== ""
                          ? searchModalChildTeam?.find((item) => {
                              return item?.value === teamValues?.childTeam;
                            })
                          : teamValues?.childTeam
                      }
                      options={
                        isModalChildTeamSearch !== ""
                          ? searchModalChildTeam
                          : ModalChildTeamData
                      }
                      onChange={(e) =>
                        this.setState({
                          teamValues: {
                            ...teamValues,
                            childTeam: e,
                          },
                        })
                      }
                    />
                  </Grid>
                </Grid>

                <Grid container>
                  <Grid item xs={3}>
                    <div style={{ marginTop: "20px", display: "flex" }}>
                      <ButtonComponent
                        className="mt-3 admin-btn-green"
                        onClick={this.handalMergeTeam}
                        color="primary"
                        value={!isLoading ? "Merge" : "Loading..."}
                        disabled={isLoading}
                      />
                    </div>
                    {createError ? (
                      <p
                        className="errorText"
                        style={{ margin: "0px 0 0 0", width: "300px" }}
                      >
                        {createError}
                      </p>
                    ) : (
                      ""
                    )}
                  </Grid>
                </Grid>
              </div>
            </Modal>
          </Grid>
        </Grid>
      </>
    );
  }
}
export default Team;
